#include "ftp.h"
#include <sys/stat.h>
#include <dirent.h>

// FTP 会话表
map_t ftp_session_table;
static uint16_t next_data_port = FTP_DATA_PORT_START;

/**
 * @brief 初始化 FTP 服务器
 */
void ftp_init() {
    map_init(&ftp_session_table, sizeof(tcp_key_t), sizeof(ftp_session_t), 0, 0, NULL, NULL);
    tcp_open(FTP_CONTROL_PORT, ftp_control_handler);
    printf("FTP Server initialized on port %d\n", FTP_CONTROL_PORT);
}

/**
 * @brief 获取或创建 FTP 会话
 */
ftp_session_t *ftp_get_session(uint8_t *src_ip, uint16_t src_port) {
    tcp_key_t key;
    memcpy(key.remote_ip, src_ip, NET_IP_LEN);
    key.remote_port = src_port;
    key.host_port = FTP_CONTROL_PORT;
    
    ftp_session_t *session = map_get(&ftp_session_table, &key);
    if (!session) {
        ftp_session_t new_session = {0};
        new_session.state = FTP_STATE_CONNECTED;
        strcpy(new_session.current_dir, "/");
        new_session.data_port = 0;
        new_session.data_conn = NULL;
        new_session.transfer_file = NULL;
        new_session.is_upload = 0;
        
        map_set(&ftp_session_table, &key, &new_session);
        session = map_get(&ftp_session_table, &key);
    }
    return session;
}

/**
 * @brief 发送 FTP 响应
 */
void ftp_send_response(tcp_conn_t *conn, const char *response, uint8_t *dst_ip, uint16_t dst_port) {
    tcp_send(conn, (uint8_t *)response, strlen(response), FTP_CONTROL_PORT, dst_ip, dst_port);
}

/**
 * @brief 解析 FTP 命令
 */
void ftp_parse_command(const char *input, ftp_command_t *cmd) {
    memset(cmd, 0, sizeof(ftp_command_t));
    
    // 查找第一个空格
    const char *space = strchr(input, ' ');
    if (space) {
        // 有参数
        size_t cmd_len = space - input;
        if (cmd_len >= FTP_CMD_MAX) cmd_len = FTP_CMD_MAX - 1;
        strncpy(cmd->cmd, input, cmd_len);
        cmd->cmd[cmd_len] = '\0';
        
        // 跳过空格，复制参数
        space++;
        strncpy(cmd->arg, space, FTP_ARG_MAX - 1);
        cmd->arg[FTP_ARG_MAX - 1] = '\0';
        
        // 移除末尾的 \r\n
        char *end = strstr(cmd->arg, "\r\n");
        if (end) *end = '\0';
    } else {
        // 无参数
        strncpy(cmd->cmd, input, FTP_CMD_MAX - 1);
        cmd->cmd[FTP_CMD_MAX - 1] = '\0';
        
        // 移除末尾的 \r\n
        char *end = strstr(cmd->cmd, "\r\n");
        if (end) *end = '\0';
    }
    
    // 转换为大写
    for (int i = 0; cmd->cmd[i]; i++) {
        if (cmd->cmd[i] >= 'a' && cmd->cmd[i] <= 'z') {
            cmd->cmd[i] = cmd->cmd[i] - 'a' + 'A';
        }
    }
}

/**
 * @brief FTP 控制连接处理函数
 */
void ftp_control_handler(tcp_conn_t *tcp_conn, uint8_t *data, size_t len, uint8_t *src_ip, uint16_t src_port) {
    if (len == 0) {
        // 新连接，发送欢迎消息
        ftp_send_response(tcp_conn, FTP_READY, src_ip, src_port);
        return;
    }
    
    // 确保数据以 null 结尾
    char buffer[FTP_BUFFER_SIZE];
    size_t copy_len = len < FTP_BUFFER_SIZE - 1 ? len : FTP_BUFFER_SIZE - 1;
    memcpy(buffer, data, copy_len);
    buffer[copy_len] = '\0';
    
    printf("FTP Command received: %s", buffer);
    
    ftp_session_t *session = ftp_get_session(src_ip, src_port);
    if (!session) {
        ftp_send_response(tcp_conn, "421 Service not available\r\n", src_ip, src_port);
        return;
    }
    
    ftp_command_t cmd;
    ftp_parse_command(buffer, &cmd);
    
    // 处理各种 FTP 命令
    if (strcmp(cmd.cmd, "USER") == 0) {
        ftp_handle_user(tcp_conn, session, cmd.arg, src_ip, src_port);
    } else if (strcmp(cmd.cmd, "PASS") == 0) {
        ftp_handle_pass(tcp_conn, session, cmd.arg, src_ip, src_port);
    } else if (strcmp(cmd.cmd, "PWD") == 0) {
        ftp_handle_pwd(tcp_conn, session, src_ip, src_port);
    } else if (strcmp(cmd.cmd, "CWD") == 0) {
        ftp_handle_cwd(tcp_conn, session, cmd.arg, src_ip, src_port);
    } else if (strcmp(cmd.cmd, "PASV") == 0) {
        ftp_handle_pasv(tcp_conn, session, src_ip, src_port);
    } else if (strcmp(cmd.cmd, "LIST") == 0) {
        ftp_handle_list(tcp_conn, session, src_ip, src_port);
    } else if (strcmp(cmd.cmd, "RETR") == 0) {
        ftp_handle_retr(tcp_conn, session, cmd.arg, src_ip, src_port);
    } else if (strcmp(cmd.cmd, "STOR") == 0) {
        ftp_handle_stor(tcp_conn, session, cmd.arg, src_ip, src_port);
    } else if (strcmp(cmd.cmd, "QUIT") == 0) {
        ftp_handle_quit(tcp_conn, session, src_ip, src_port);
    } else {
        ftp_send_response(tcp_conn, FTP_UNKNOWN, src_ip, src_port);
    }
}

/**
 * @brief 处理 USER 命令
 */
void ftp_handle_user(tcp_conn_t *conn, ftp_session_t *session, const char *username, uint8_t *src_ip, uint16_t src_port) {
    if (strlen(username) == 0) {
        ftp_send_response(conn, FTP_SYNTAX_ERROR, src_ip, src_port);
        return;
    }
    
    strncpy(session->username, username, sizeof(session->username) - 1);
    session->username[sizeof(session->username) - 1] = '\0';
    session->state = FTP_STATE_USER_SENT;
    
    ftp_send_response(conn, FTP_USER_OK, src_ip, src_port);
}

/**
 * @brief 处理 PASS 命令
 */
void ftp_handle_pass(tcp_conn_t *conn, ftp_session_t *session, const char *password, uint8_t *src_ip, uint16_t src_port) {
    if (session->state != FTP_STATE_USER_SENT) {
        ftp_send_response(conn, "503 Login with USER first\r\n", src_ip, src_port);
        return;
    }
    
    // 简单的用户验证（实际应用中应该更安全）
    if (strcmp(session->username, "anonymous") == 0 || strcmp(session->username, "ftp") == 0) {
        session->state = FTP_STATE_LOGGED_IN;
        ftp_send_response(conn, FTP_LOGIN_OK, src_ip, src_port);
    } else {
        ftp_send_response(conn, FTP_LOGIN_FAIL, src_ip, src_port);
    }
}

/**
 * @brief 处理 PWD 命令
 */
void ftp_handle_pwd(tcp_conn_t *conn, ftp_session_t *session, uint8_t *src_ip, uint16_t src_port) {
    if (session->state < FTP_STATE_LOGGED_IN) {
        ftp_send_response(conn, "530 Please login with USER and PASS\r\n", src_ip, src_port);
        return;
    }

    char response[256];
    snprintf(response, sizeof(response), FTP_PWD_REPLY, session->current_dir);
    ftp_send_response(conn, response, src_ip, src_port);
}

/**
 * @brief 处理 CWD 命令
 */
void ftp_handle_cwd(tcp_conn_t *conn, ftp_session_t *session, const char *path, uint8_t *src_ip, uint16_t src_port) {
    if (session->state < FTP_STATE_LOGGED_IN) {
        ftp_send_response(conn, "530 Please login with USER and PASS\r\n", src_ip, src_port);
        return;
    }

    if (strlen(path) == 0) {
        ftp_send_response(conn, FTP_SYNTAX_ERROR, src_ip, src_port);
        return;
    }

    char new_path[FTP_PATH_MAX];
    if (path[0] == '/') {
        // 绝对路径
        strncpy(new_path, path, FTP_PATH_MAX - 1);
    } else {
        // 相对路径
        snprintf(new_path, FTP_PATH_MAX, "%s/%s", session->current_dir, path);
    }
    new_path[FTP_PATH_MAX - 1] = '\0';

    // 简化路径（移除 . 和 ..）
    // 这里简化实现，实际应用中需要更完善的路径处理
    struct stat st;
    if (stat(new_path, &st) == 0 && S_ISDIR(st.st_mode)) {
        strncpy(session->current_dir, new_path, FTP_PATH_MAX - 1);
        session->current_dir[FTP_PATH_MAX - 1] = '\0';
        ftp_send_response(conn, FTP_CWD_OK, src_ip, src_port);
    } else {
        ftp_send_response(conn, FTP_CWD_FAIL, src_ip, src_port);
    }
}

/**
 * @brief 处理 PASV 命令
 */
void ftp_handle_pasv(tcp_conn_t *conn, ftp_session_t *session, uint8_t *src_ip, uint16_t src_port) {
    if (session->state < FTP_STATE_LOGGED_IN) {
        ftp_send_response(conn, "530 Please login with USER and PASS\r\n", src_ip, src_port);
        return;
    }

    // 分配数据端口
    session->data_port = next_data_port++;
    if (next_data_port > FTP_DATA_PORT_END) {
        next_data_port = FTP_DATA_PORT_START;
    }

    // 打开数据端口监听
    tcp_open(session->data_port, ftp_data_handler);

    session->state = FTP_STATE_PASV_MODE;

    // 发送 PASV 响应
    char response[256];
    uint8_t *ip = net_if_ip;
    uint16_t port = session->data_port;
    snprintf(response, sizeof(response), FTP_PASV_OK,
             ip[0], ip[1], ip[2], ip[3],
             (port >> 8) & 0xFF, port & 0xFF);
    ftp_send_response(conn, response, src_ip, src_port);
}

/**
 * @brief 检查文件权限
 */
int ftp_check_file_permissions(const char *filepath, int is_write) {
    struct stat st;
    if (stat(filepath, &st) != 0) {
        return is_write ? 1 : 0; // 写操作时文件不存在是正常的
    }

    if (is_write) {
        return (st.st_mode & S_IWUSR) ? 1 : 0;
    } else {
        return (st.st_mode & S_IRUSR) ? 1 : 0;
    }
}

/**
 * @brief 获取文件大小
 */
long ftp_get_file_size(const char *filepath) {
    struct stat st;
    if (stat(filepath, &st) == 0) {
        return st.st_size;
    }
    return -1;
}

/**
 * @brief 处理 LIST 命令
 */
void ftp_handle_list(tcp_conn_t *conn, ftp_session_t *session, uint8_t *src_ip, uint16_t src_port) {
    if (session->state < FTP_STATE_LOGGED_IN) {
        ftp_send_response(conn, "530 Please login with USER and PASS\r\n", src_ip, src_port);
        return;
    }

    if (session->state != FTP_STATE_PASV_MODE) {
        ftp_send_response(conn, "425 Use PASV first\r\n", src_ip, src_port);
        return;
    }

    ftp_send_response(conn, FTP_LIST_OK, src_ip, src_port);

    // 这里应该通过数据连接发送目录列表
    // 由于简化实现，我们直接在控制连接上发送完成消息
    ftp_send_response(conn, FTP_LIST_END, src_ip, src_port);

    // 关闭数据端口
    tcp_close(session->data_port);
    session->state = FTP_STATE_LOGGED_IN;
}

/**
 * @brief 处理 RETR 命令（下载文件）
 */
void ftp_handle_retr(tcp_conn_t *conn, ftp_session_t *session, const char *filename, uint8_t *src_ip, uint16_t src_port) {
    if (session->state < FTP_STATE_LOGGED_IN) {
        ftp_send_response(conn, "530 Please login with USER and PASS\r\n", src_ip, src_port);
        return;
    }

    if (session->state != FTP_STATE_PASV_MODE) {
        ftp_send_response(conn, "425 Use PASV first\r\n", src_ip, src_port);
        return;
    }

    if (strlen(filename) == 0) {
        ftp_send_response(conn, FTP_SYNTAX_ERROR, src_ip, src_port);
        return;
    }

    char filepath[FTP_PATH_MAX];
    if (filename[0] == '/') {
        strncpy(filepath, filename, FTP_PATH_MAX - 1);
    } else {
        snprintf(filepath, FTP_PATH_MAX, "%s/%s", session->current_dir, filename);
    }
    filepath[FTP_PATH_MAX - 1] = '\0';

    // 检查文件是否存在和可读
    if (!ftp_check_file_permissions(filepath, 0)) {
        ftp_send_response(conn, FTP_RETR_FAIL, src_ip, src_port);
        tcp_close(session->data_port);
        session->state = FTP_STATE_LOGGED_IN;
        return;
    }

    // 打开文件
    session->transfer_file = fopen(filepath, "rb");
    if (!session->transfer_file) {
        ftp_send_response(conn, FTP_RETR_FAIL, src_ip, src_port);
        tcp_close(session->data_port);
        session->state = FTP_STATE_LOGGED_IN;
        return;
    }

    session->is_upload = 0;
    session->state = FTP_STATE_DATA_TRANSFER;

    char response[256];
    snprintf(response, sizeof(response), FTP_RETR_OK, filename);
    ftp_send_response(conn, response, src_ip, src_port);
}

/**
 * @brief 处理 STOR 命令（上传文件）
 */
void ftp_handle_stor(tcp_conn_t *conn, ftp_session_t *session, const char *filename, uint8_t *src_ip, uint16_t src_port) {
    if (session->state < FTP_STATE_LOGGED_IN) {
        ftp_send_response(conn, "530 Please login with USER and PASS\r\n", src_ip, src_port);
        return;
    }

    if (session->state != FTP_STATE_PASV_MODE) {
        ftp_send_response(conn, "425 Use PASV first\r\n", src_ip, src_port);
        return;
    }

    if (strlen(filename) == 0) {
        ftp_send_response(conn, FTP_SYNTAX_ERROR, src_ip, src_port);
        return;
    }

    char filepath[FTP_PATH_MAX];
    if (filename[0] == '/') {
        strncpy(filepath, filename, FTP_PATH_MAX - 1);
    } else {
        snprintf(filepath, FTP_PATH_MAX, "%s/%s", session->current_dir, filename);
    }
    filepath[FTP_PATH_MAX - 1] = '\0';

    // 检查目录是否可写
    char dir_path[FTP_PATH_MAX];
    strncpy(dir_path, filepath, FTP_PATH_MAX - 1);
    char *last_slash = strrchr(dir_path, '/');
    if (last_slash) {
        *last_slash = '\0';
    } else {
        strcpy(dir_path, ".");
    }

    if (!ftp_check_file_permissions(dir_path, 1)) {
        ftp_send_response(conn, FTP_STOR_FAIL, src_ip, src_port);
        tcp_close(session->data_port);
        session->state = FTP_STATE_LOGGED_IN;
        return;
    }

    // 创建文件
    session->transfer_file = fopen(filepath, "wb");
    if (!session->transfer_file) {
        ftp_send_response(conn, FTP_STOR_FAIL, src_ip, src_port);
        tcp_close(session->data_port);
        session->state = FTP_STATE_LOGGED_IN;
        return;
    }

    session->is_upload = 1;
    session->state = FTP_STATE_DATA_TRANSFER;

    ftp_send_response(conn, FTP_STOR_OK, src_ip, src_port);
}

/**
 * @brief 处理 QUIT 命令
 */
void ftp_handle_quit(tcp_conn_t *conn, ftp_session_t *session, uint8_t *src_ip, uint16_t src_port) {
    ftp_send_response(conn, FTP_QUIT, src_ip, src_port);

    // 清理会话
    if (session->transfer_file) {
        fclose(session->transfer_file);
        session->transfer_file = NULL;
    }

    if (session->data_port > 0) {
        tcp_close(session->data_port);
    }

    // 从会话表中删除
    tcp_key_t key;
    memcpy(key.remote_ip, src_ip, NET_IP_LEN);
    key.remote_port = src_port;
    key.host_port = FTP_CONTROL_PORT;
    map_delete(&ftp_session_table, &key);
}

/**
 * @brief FTP 数据连接处理函数
 */
void ftp_data_handler(tcp_conn_t *tcp_conn, uint8_t *data, size_t len, uint8_t *src_ip, uint16_t src_port) {
    // 查找对应的会话
    // 这里简化实现，实际应该根据数据端口找到对应的控制连接会话
    printf("FTP Data received: %zu bytes\n", len);

    // 这里应该处理文件传输逻辑
    // 由于简化实现，暂时只打印接收到的数据
}
