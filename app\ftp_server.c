#include "driver.h"
#include "net.h"
#include "ftp.h"

#ifdef TCP
#include "tcp.h"

int main(int argc, char const *argv[]) {
    printf("Starting Simple FTP Server...\n");
    
    if (net_init() == -1) {  // 初始化协议栈
        printf("net init failed.\n");
        return -1;
    }
    
    // 初始化 FTP 服务器
    ftp_init();
    
    printf("FTP Server is running...\n");
    printf("Control Port: %d\n", FTP_CONTROL_PORT);
    printf("Data Port Range: %d - %d\n", FTP_DATA_PORT_START, FTP_DATA_PORT_END);
    printf("Current Directory: %s\n", ".");
    printf("Supported Commands: USER, PASS, PWD, CWD, PASV, LIST, RETR, STOR, QUIT\n");
    printf("Default Users: anonymous, ftp (no password required)\n");
    printf("\nPress Ctrl+C to stop the server.\n\n");
    
    while (1) {
        net_poll();  // 一次主循环
    }
    
    return 0;
}

#else
int main(int argc, char const *argv[]) {
    printf("Error: TCP support is not enabled. Please compile with TCP flag.\n");
    return -1;
}
#endif
