#include "icmp.h"

#include "ip.h"
#include "net.h"

/**
 * @brief 发送icmp响应
 *
 * @param req_buf 收到的icmp请求包
 * @param src_ip 源ip地址
 */
static void icmp_resp(buf_t *req_buf, uint8_t *src_ip) {
    // Step1: 初始化并封装数据
    // 初始化发送缓冲区
    buf_init(&txbuf, req_buf->len);
    
    // 拷贝接收到的ICMP数据
    memcpy(txbuf.data, req_buf->data, req_buf->len);
    
    // 获取ICMP头部
    icmp_hdr_t *req_icmp_hdr = (icmp_hdr_t *)req_buf->data;
    icmp_hdr_t *resp_icmp_hdr = (icmp_hdr_t *)txbuf.data;
    
    // 修改ICMP类型为回显应答
    resp_icmp_hdr->type = ICMP_TYPE_ECHO_REPLY;
    resp_icmp_hdr->code = 0;
    
    // Step2: 填写校验和
    // 先将校验和字段清零
    resp_icmp_hdr->checksum16 = 0;
    // 计算校验和
    resp_icmp_hdr->checksum16 = checksum16((uint16_t *)txbuf.data, txbuf.len);
    
    // Step3: 发送数据报
    ip_out(&txbuf, src_ip, NET_PROTOCOL_ICMP);
}

/**
 * @brief 处理一个收到的数据包
 *
 * @param buf 要处理的数据包
 * @param src_ip 源ip地址
 */
void icmp_in(buf_t *buf, uint8_t *src_ip) {
    // Step1: 报头检测
    if (buf->len < sizeof(icmp_hdr_t)) {
        // 数据包不完整,直接丢弃
        return;
    }

    // 获取ICMP头部
    icmp_hdr_t *icmp_hdr = (icmp_hdr_t *)buf->data;

    // Step2: 查看ICMP类型
    // Step3: 如果是回显请求,则回送回显应答
    if (icmp_hdr->type == ICMP_TYPE_ECHO_REQUEST) {
        icmp_resp(buf, src_ip);
    }
}

/**
 * @brief 发送icmp不可达
 *
 * @param recv_buf 收到的ip数据包
 * @param src_ip 源ip地址
 * @param code icmp code，协议不可达或端口不可达
 */
void icmp_unreachable(buf_t *recv_buf, uint8_t *src_ip, icmp_code_t code) {
    // Step1: 初始化并填写报头
    // ICMP差错报文需要包含IP头部和IP数据的前8个字节
    // 计算需要的缓冲区大小: ICMP头部 + IP头部 + 8字节数据
    size_t ip_hdr_len = sizeof(ip_hdr_t);
    size_t data_len = 8; // ICMP差错报文数据字段包含IP数据报的前8个字节
    
    // 初始化发送缓冲区
    buf_init(&txbuf, sizeof(icmp_hdr_t) + ip_hdr_len + data_len);
    
    // 填写ICMP头部
    icmp_hdr_t *icmp_hdr = (icmp_hdr_t *)txbuf.data;
    icmp_hdr->type = ICMP_TYPE_UNREACH;
    icmp_hdr->code = code;
    icmp_hdr->id16 = 0;
    icmp_hdr->seq16 = 0;
    
    // Step2: 填写数据与校验和
    // 拷贝IP头部和数据的前8个字节到ICMP报文的数据字段
    // ICMP头部后面是IP头部和数据
    uint8_t *icmp_data = txbuf.data + sizeof(icmp_hdr_t);
    
    // 拷贝IP头部
    memcpy(icmp_data, recv_buf->data, ip_hdr_len);
    
    // 拷贝IP数据的前8个字节(如果有)
    if (recv_buf->len > ip_hdr_len) {
        size_t copy_len = (recv_buf->len - ip_hdr_len) < data_len ? 
                          (recv_buf->len - ip_hdr_len) : data_len;
        memcpy(icmp_data + ip_hdr_len, recv_buf->data + ip_hdr_len, copy_len);
    }
    
    // 计算校验和
    icmp_hdr->checksum16 = 0;
    icmp_hdr->checksum16 = checksum16((uint16_t *)txbuf.data, txbuf.len);
    
    // Step3: 发送数据报
    ip_out(&txbuf, src_ip, NET_PROTOCOL_ICMP);
}

/**
 * @brief 初始化icmp协议
 *
 */
void icmp_init() {
    net_add_protocol(NET_PROTOCOL_ICMP, icmp_in);
}
