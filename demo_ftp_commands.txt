FTP服务器功能演示命令序列
================================

1. 启动FTP服务器
   cd build
   .\ftp_server.exe

2. 服务器启动后显示信息：
   Starting Simple FTP Server...
   Using interface \Device\NPF_{...}, my ip is **************.
   FTP Server initialized on port 21
   FTP Server is running...
   Control Port: 21
   Data Port Range: 20000 - 21000

3. 使用telnet测试FTP命令（在新的命令行窗口）：
   telnet ************** 21

4. FTP命令测试序列：

   a) 连接成功后收到欢迎消息：
      220 Simple FTP Server ready

   b) 用户登录：
      USER anonymous
      期望响应: 331 User name okay, need password
      
      PASS 
      期望响应: 230 User logged in, proceed

   c) 目录操作：
      PWD
      期望响应: 257 "/" is current directory
      
      CWD /tmp
      期望响应: 250 Directory successfully changed 或 550 Failed to change directory
      
      PWD
      期望响应: 257 "/tmp" is current directory

   d) 被动模式：
      PASV
      期望响应: 227 Entering Passive Mode (10,250,179,167,78,32)

   e) 文件列表：
      LIST
      期望响应: 150 Here comes the directory listing
                226 Directory send OK

   f) 文件下载：
      PASV
      RETR test.txt
      期望响应: 150 Opening BINARY mode data connection for test.txt
                226 Transfer complete
      或者: 550 Failed to open file

   g) 文件上传：
      PASV
      STOR upload.txt
      期望响应: 150 Ok to send data
                226 Transfer complete
      或者: 550 Failed to create file

   h) 错误测试：
      INVALID
      期望响应: 500 Unknown command
      
      USER
      期望响应: 501 Syntax error in parameters or arguments

   i) 退出：
      QUIT
      期望响应: 221 Goodbye

5. 预期的服务器行为：
   - 正确解析和响应所有FTP命令
   - 维护用户会话状态
   - 处理文件权限检查
   - 提供适当的错误消息
   - 管理被动模式数据端口

6. 测试文件准备：
   在服务器运行目录创建测试文件：
   echo "This is a test file" > test.txt

7. 验证要点：
   ✓ 服务器启动无错误
   ✓ 接受TCP连接
   ✓ 发送正确的FTP响应码
   ✓ 状态转换正确
   ✓ 命令解析准确
   ✓ 错误处理适当
   ✓ 会话管理正常

8. 成功标准：
   - 所有基本FTP命令都能正确响应
   - 用户认证流程完整
   - 文件操作命令语法正确
   - 错误情况处理得当
   - 服务器保持稳定运行
