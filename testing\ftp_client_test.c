#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windows.h>

#pragma comment(lib, "ws2_32.lib")

#define BUFFER_SIZE 1024
#define FTP_PORT 21

typedef struct {
    SOCKET control_socket;
    char server_ip[16];
    int server_port;
    int connected;
    char response_buffer[BUFFER_SIZE];
} ftp_client_t;

// 函数声明
int init_winsock();
void cleanup_winsock();
int ftp_connect(ftp_client_t *client, const char *host, int port);
int ftp_send_command(ftp_client_t *client, const char *command);
int ftp_receive_response(ftp_client_t *client);
int test_ftp_login(ftp_client_t *client, const char *username, const char *password);
void test_directory_operations(ftp_client_t *client);
void test_passive_mode(ftp_client_t *client);
void test_file_operations(ftp_client_t *client);
void test_error_handling(ftp_client_t *client);
void ftp_disconnect(ftp_client_t *client);
void create_test_file();

/**
 * @brief 初始化Winsock
 */
int init_winsock() {
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        printf("WSAStartup failed: %d\n", result);
        return 0;
    }
    return 1;
}

/**
 * @brief 清理Winsock
 */
void cleanup_winsock() {
    WSACleanup();
}

/**
 * @brief 连接到FTP服务器
 */
int ftp_connect(ftp_client_t *client, const char *host, int port) {
    struct sockaddr_in server_addr;
    
    // 创建socket
    client->control_socket = socket(AF_INET, SOCK_STREAM, 0);
    if (client->control_socket == INVALID_SOCKET) {
        printf("Socket creation failed: %d\n", WSAGetLastError());
        return 0;
    }
    
    // 设置服务器地址
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    inet_pton(AF_INET, host, &server_addr.sin_addr);
    
    // 连接到服务器
    if (connect(client->control_socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
        printf("Connection failed: %d\n", WSAGetLastError());
        closesocket(client->control_socket);
        return 0;
    }
    
    strcpy(client->server_ip, host);
    client->server_port = port;
    client->connected = 1;
    
    // 接收欢迎消息
    ftp_receive_response(client);
    
    return 1;
}

/**
 * @brief 发送FTP命令
 */
int ftp_send_command(ftp_client_t *client, const char *command) {
    if (!client->connected) {
        printf("Error: Not connected to FTP server\n");
        return 0;
    }
    
    char cmd_buffer[BUFFER_SIZE];
    snprintf(cmd_buffer, sizeof(cmd_buffer), "%s\r\n", command);
    
    int bytes_sent = send(client->control_socket, cmd_buffer, strlen(cmd_buffer), 0);
    if (bytes_sent == SOCKET_ERROR) {
        printf("Send failed: %d\n", WSAGetLastError());
        return 0;
    }
    
    printf("Sent: %s", cmd_buffer);
    return 1;
}

/**
 * @brief 接收FTP响应
 */
int ftp_receive_response(ftp_client_t *client) {
    if (!client->connected) {
        return 0;
    }
    
    memset(client->response_buffer, 0, sizeof(client->response_buffer));
    
    int bytes_received = recv(client->control_socket, client->response_buffer, 
                             sizeof(client->response_buffer) - 1, 0);
    
    if (bytes_received == SOCKET_ERROR) {
        printf("Receive failed: %d\n", WSAGetLastError());
        return 0;
    }
    
    if (bytes_received > 0) {
        client->response_buffer[bytes_received] = '\0';
        printf("Received: %s", client->response_buffer);
    }
    
    return bytes_received;
}

/**
 * @brief 测试FTP登录
 */
int test_ftp_login(ftp_client_t *client, const char *username, const char *password) {
    printf("\n=== Testing FTP Login ===\n");
    
    // 发送USER命令
    char user_cmd[128];
    snprintf(user_cmd, sizeof(user_cmd), "USER %s", username);
    if (!ftp_send_command(client, user_cmd)) {
        return 0;
    }
    ftp_receive_response(client);
    
    // 发送PASS命令
    char pass_cmd[128];
    snprintf(pass_cmd, sizeof(pass_cmd), "PASS %s", password);
    if (!ftp_send_command(client, pass_cmd)) {
        return 0;
    }
    ftp_receive_response(client);
    
    // 检查是否登录成功
    if (strstr(client->response_buffer, "230") != NULL) {
        printf("✓ Login successful\n");
        return 1;
    } else {
        printf("✗ Login failed\n");
        return 0;
    }
}

/**
 * @brief 测试目录操作
 */
void test_directory_operations(ftp_client_t *client) {
    printf("\n=== Testing Directory Operations ===\n");
    
    // 测试PWD命令
    printf("\n1. Testing PWD command:\n");
    ftp_send_command(client, "PWD");
    ftp_receive_response(client);
    
    // 测试CWD命令
    printf("\n2. Testing CWD command:\n");
    ftp_send_command(client, "CWD /tmp");
    ftp_receive_response(client);
    
    // 再次测试PWD
    printf("\n3. Testing PWD after CWD:\n");
    ftp_send_command(client, "PWD");
    ftp_receive_response(client);
}

/**
 * @brief 测试被动模式
 */
void test_passive_mode(ftp_client_t *client) {
    printf("\n=== Testing Passive Mode ===\n");
    
    ftp_send_command(client, "PASV");
    ftp_receive_response(client);
    
    if (strstr(client->response_buffer, "227") != NULL) {
        printf("✓ PASV mode enabled\n");
    } else {
        printf("✗ PASV mode failed\n");
    }
}

/**
 * @brief 测试文件操作
 */
void test_file_operations(ftp_client_t *client) {
    printf("\n=== Testing File Operations ===\n");
    
    // 测试LIST命令
    printf("\n1. Testing LIST command:\n");
    ftp_send_command(client, "PASV");
    ftp_receive_response(client);
    ftp_send_command(client, "LIST");
    ftp_receive_response(client);
    
    // 测试RETR命令
    printf("\n2. Testing RETR command:\n");
    ftp_send_command(client, "PASV");
    ftp_receive_response(client);
    ftp_send_command(client, "RETR test.txt");
    ftp_receive_response(client);
    
    // 测试STOR命令
    printf("\n3. Testing STOR command:\n");
    ftp_send_command(client, "PASV");
    ftp_receive_response(client);
    ftp_send_command(client, "STOR upload_test.txt");
    ftp_receive_response(client);
}

/**
 * @brief 测试错误处理
 */
void test_error_handling(ftp_client_t *client) {
    printf("\n=== Testing Error Handling ===\n");
    
    // 测试无效命令
    printf("\n1. Testing invalid command:\n");
    ftp_send_command(client, "INVALID");
    ftp_receive_response(client);
    
    // 测试语法错误
    printf("\n2. Testing syntax errors:\n");
    ftp_send_command(client, "USER");
    ftp_receive_response(client);
    
    ftp_send_command(client, "CWD");
    ftp_receive_response(client);
}

/**
 * @brief 断开FTP连接
 */
void ftp_disconnect(ftp_client_t *client) {
    if (client->connected) {
        printf("\n=== Disconnecting ===\n");
        ftp_send_command(client, "QUIT");
        ftp_receive_response(client);
        
        closesocket(client->control_socket);
        client->connected = 0;
    }
}

/**
 * @brief 创建测试文件
 */
void create_test_file() {
    FILE *file = fopen("test_upload.txt", "w");
    if (file) {
        fprintf(file, "This is a test file for FTP upload.\n");
        fprintf(file, "Line 2 of the test file.\n");
        fprintf(file, "End of test file.\n");
        fclose(file);
        printf("Created test file: test_upload.txt\n");
    }
}

/**
 * @brief 主函数
 */
int main() {
    printf("FTP Client Test Program\n");
    printf("=======================\n");
    
    // 初始化Winsock
    if (!init_winsock()) {
        return 1;
    }
    
    // 创建测试文件
    create_test_file();
    
    // 创建FTP客户端
    ftp_client_t client = {0};
    
    // 连接到FTP服务器
    printf("\nConnecting to FTP server at 10.250.179.167:21...\n");
    if (!ftp_connect(&client, "10.250.179.167", FTP_PORT)) {
        printf("Failed to connect to FTP server\n");
        cleanup_winsock();
        return 1;
    }
    
    printf("✓ Connected to FTP server\n");
    
    // 运行测试
    if (test_ftp_login(&client, "anonymous", "")) {
        test_directory_operations(&client);
        test_passive_mode(&client);
        test_file_operations(&client);
        test_error_handling(&client);
    }
    
    // 断开连接
    ftp_disconnect(&client);
    
    // 清理
    cleanup_winsock();
    
    printf("\n=== All tests completed ===\n");
    printf("Press any key to exit...\n");
    getchar();
    
    return 0;
}
