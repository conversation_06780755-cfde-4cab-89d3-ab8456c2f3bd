#include "driver.h"
#include "net.h"
#include "ftp.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#ifdef TCP
#include "tcp.h"

// 测试用的FTP客户端连接信息
typedef struct {
    tcp_conn_t *conn;
    uint8_t server_ip[NET_IP_LEN];
    uint16_t server_port;
    int connected;
    char response_buffer[1024];
} ftp_client_t;

static ftp_client_t test_client = {0};

/**
 * @brief 测试客户端接收响应的处理函数
 */
void test_client_handler(tcp_conn_t *tcp_conn, uint8_t *data, size_t len, uint8_t *src_ip, uint16_t src_port) {
    if (len > 0) {
        size_t copy_len = len < sizeof(test_client.response_buffer) - 1 ? len : sizeof(test_client.response_buffer) - 1;
        memcpy(test_client.response_buffer, data, copy_len);
        test_client.response_buffer[copy_len] = '\0';
        printf("Server Response: %s", test_client.response_buffer);
    }
}

/**
 * @brief 发送FTP命令到服务器
 */
void send_ftp_command(const char *command) {
    if (!test_client.connected) {
        printf("Error: Not connected to FTP server\n");
        return;
    }
    
    printf("Sending command: %s", command);
    tcp_send(test_client.conn, (uint8_t *)command, strlen(command), 12345, test_client.server_ip, test_client.server_port);
    
    // 等待响应
    for (int i = 0; i < 10; i++) {
        net_poll();
        usleep(100000); // 100ms
    }
}

/**
 * @brief 测试基本FTP命令
 */
void test_ftp_commands() {
    printf("\n=== Testing FTP Commands ===\n");
    
    // 测试 USER 命令
    printf("\n1. Testing USER command:\n");
    send_ftp_command("USER anonymous\r\n");
    
    // 测试 PASS 命令
    printf("\n2. Testing PASS command:\n");
    send_ftp_command("PASS \r\n");
    
    // 测试 PWD 命令
    printf("\n3. Testing PWD command:\n");
    send_ftp_command("PWD\r\n");
    
    // 测试 CWD 命令
    printf("\n4. Testing CWD command:\n");
    send_ftp_command("CWD /tmp\r\n");
    
    // 再次测试 PWD
    printf("\n5. Testing PWD after CWD:\n");
    send_ftp_command("PWD\r\n");
    
    // 测试 PASV 命令
    printf("\n6. Testing PASV command:\n");
    send_ftp_command("PASV\r\n");
    
    // 测试 LIST 命令
    printf("\n7. Testing LIST command:\n");
    send_ftp_command("LIST\r\n");
    
    // 测试无效命令
    printf("\n8. Testing invalid command:\n");
    send_ftp_command("INVALID\r\n");
    
    // 测试 QUIT 命令
    printf("\n9. Testing QUIT command:\n");
    send_ftp_command("QUIT\r\n");
}

/**
 * @brief 测试文件操作
 */
void test_file_operations() {
    printf("\n=== Testing File Operations ===\n");
    
    // 重新登录
    send_ftp_command("USER ftp\r\n");
    send_ftp_command("PASS \r\n");
    
    // 创建测试文件
    FILE *test_file = fopen("test_upload.txt", "w");
    if (test_file) {
        fprintf(test_file, "This is a test file for FTP upload.\n");
        fprintf(test_file, "Line 2 of the test file.\n");
        fprintf(test_file, "End of test file.\n");
        fclose(test_file);
        printf("Created test file: test_upload.txt\n");
    }
    
    // 测试文件下载
    printf("\n1. Testing file download (RETR):\n");
    send_ftp_command("PASV\r\n");
    send_ftp_command("RETR test_upload.txt\r\n");
    
    // 测试文件上传
    printf("\n2. Testing file upload (STOR):\n");
    send_ftp_command("PASV\r\n");
    send_ftp_command("STOR test_download.txt\r\n");
    
    // 测试不存在的文件
    printf("\n3. Testing non-existent file:\n");
    send_ftp_command("PASV\r\n");
    send_ftp_command("RETR nonexistent.txt\r\n");
}

/**
 * @brief 测试错误处理
 */
void test_error_handling() {
    printf("\n=== Testing Error Handling ===\n");
    
    // 测试未登录状态下的命令
    printf("\n1. Testing commands without login:\n");
    send_ftp_command("PWD\r\n");
    send_ftp_command("LIST\r\n");
    
    // 测试错误的登录
    printf("\n2. Testing invalid login:\n");
    send_ftp_command("USER invaliduser\r\n");
    send_ftp_command("PASS wrongpass\r\n");
    
    // 测试语法错误
    printf("\n3. Testing syntax errors:\n");
    send_ftp_command("USER\r\n");  // 缺少参数
    send_ftp_command("CWD\r\n");   // 缺少参数
}

/**
 * @brief 运行所有测试
 */
void run_all_tests() {
    printf("Starting FTP Server Tests...\n");
    
    // 等待服务器启动
    printf("Waiting for server to start...\n");
    for (int i = 0; i < 50; i++) {
        net_poll();
        usleep(100000); // 100ms
    }
    
    test_ftp_commands();
    test_file_operations();
    test_error_handling();
    
    printf("\n=== All Tests Completed ===\n");
}

int main(int argc, char const *argv[]) {
    printf("FTP Server Test Client\n");
    printf("======================\n");
    
    if (net_init() == -1) {
        printf("net init failed.\n");
        return -1;
    }
    
    // 初始化FTP服务器（在同一进程中测试）
    ftp_init();
    
    // 设置测试客户端
    test_client.server_ip[0] = 127;
    test_client.server_ip[1] = 0;
    test_client.server_ip[2] = 0;
    test_client.server_ip[3] = 1;
    test_client.server_port = FTP_CONTROL_PORT;
    test_client.connected = 1;
    
    // 注册客户端处理函数
    tcp_open(12345, test_client_handler);
    
    // 运行测试
    run_all_tests();
    
    return 0;
}

#else
int main(int argc, char const *argv[]) {
    printf("Error: TCP support is not enabled. Please compile with TCP flag.\n");
    return -1;
}
#endif
