#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>

// 简化的FTP测试，只测试核心逻辑
// 避免依赖整个网络协议栈

// 测试结果统计
static int tests_passed = 0;
static int tests_failed = 0;

// FTP命令结构（从ftp.h复制）
#define FTP_CMD_MAX 64
#define FTP_ARG_MAX 192

typedef struct {
    char cmd[FTP_CMD_MAX];
    char arg[FTP_ARG_MAX];
} ftp_command_t;

// 简化的FTP命令解析函数（从ftp.c复制核心逻辑）
void ftp_parse_command(const char *input, ftp_command_t *cmd) {
    memset(cmd, 0, sizeof(ftp_command_t));

    // 查找第一个空格
    const char *space = strchr(input, ' ');
    if (space) {
        // 有参数
        size_t cmd_len = space - input;
        if (cmd_len >= FTP_CMD_MAX) cmd_len = FTP_CMD_MAX - 1;
        strncpy(cmd->cmd, input, cmd_len);
        cmd->cmd[cmd_len] = '\0';

        // 跳过空格，复制参数
        space++;
        strncpy(cmd->arg, space, FTP_ARG_MAX - 1);
        cmd->arg[FTP_ARG_MAX - 1] = '\0';

        // 移除末尾的 \r\n
        char *end = strstr(cmd->arg, "\r\n");
        if (end) *end = '\0';
    } else {
        // 无参数
        strncpy(cmd->cmd, input, FTP_CMD_MAX - 1);
        cmd->cmd[FTP_CMD_MAX - 1] = '\0';

        // 移除末尾的 \r\n
        char *end = strstr(cmd->cmd, "\r\n");
        if (end) *end = '\0';
    }

    // 转换为大写
    for (int i = 0; cmd->cmd[i]; i++) {
        if (cmd->cmd[i] >= 'a' && cmd->cmd[i] <= 'z') {
            cmd->cmd[i] = cmd->cmd[i] - 'a' + 'A';
        }
    }
}

/**
 * @brief 测试辅助函数 - 检查测试结果
 */
void check_test(const char* test_name, int condition) {
    if (condition) {
        printf("✓ %s\n", test_name);
        tests_passed++;
    } else {
        printf("✗ %s\n", test_name);
        tests_failed++;
    }
}

/**
 * @brief 测试FTP命令解析功能
 */
void test_ftp_command_parsing() {
    printf("\n=== Testing FTP Command Parsing ===\n");

    ftp_command_t cmd;

    // 测试USER命令
    ftp_parse_command("USER anonymous\r\n", &cmd);
    check_test("Parse USER command", strcmp(cmd.cmd, "USER") == 0 && strcmp(cmd.arg, "anonymous") == 0);

    // 测试PASS命令
    ftp_parse_command("PASS password123\r\n", &cmd);
    check_test("Parse PASS command", strcmp(cmd.cmd, "PASS") == 0 && strcmp(cmd.arg, "password123") == 0);

    // 测试无参数命令
    ftp_parse_command("PWD\r\n", &cmd);
    check_test("Parse PWD command", strcmp(cmd.cmd, "PWD") == 0 && strlen(cmd.arg) == 0);

    // 测试大小写转换
    ftp_parse_command("list\r\n", &cmd);
    check_test("Command case conversion", strcmp(cmd.cmd, "LIST") == 0);
}

/**
 * @brief 测试字符串处理功能
 */
void test_string_processing() {
    printf("\n=== Testing String Processing ===\n");

    // 测试字符串比较
    check_test("String comparison", strcmp("USER", "USER") == 0);
    check_test("Case insensitive check", strcmp("user", "USER") != 0);

    // 测试字符串长度
    check_test("String length", strlen("anonymous") == 9);
    check_test("Empty string", strlen("") == 0);

    // 测试字符串复制
    char buffer[64];
    strcpy(buffer, "test");
    check_test("String copy", strcmp(buffer, "test") == 0);
}

/**
 * @brief 测试文件操作
 */
void test_file_operations() {
    printf("\n=== Testing File Operations ===\n");

    // 创建测试文件
    FILE *test_file = fopen("test_file.txt", "w");
    if (test_file) {
        fprintf(test_file, "Test content\n");
        fclose(test_file);

        // 测试文件是否存在
        FILE *check_file = fopen("test_file.txt", "r");
        check_test("File creation and access", check_file != NULL);
        if (check_file) {
            fclose(check_file);
        }

        // 清理测试文件
        int remove_result = remove("test_file.txt");
        check_test("File removal", remove_result == 0);
    } else {
        printf("Warning: Could not create test file\n");
    }
}

/**
 * @brief 主测试函数
 */
int main(int argc, char *argv[]) {
    printf("FTP Server Unit Tests\n");
    printf("=====================\n");

    // 运行各项测试
    test_ftp_command_parsing();
    test_string_processing();
    test_file_operations();

    // 输出测试结果
    printf("\n=== Test Results ===\n");
    printf("Tests passed: %d\n", tests_passed);
    printf("Tests failed: %d\n", tests_failed);
    printf("Total tests: %d\n", tests_passed + tests_failed);

    if (tests_failed == 0) {
        printf("✓ All tests passed!\n");
        return 0;
    } else {
        printf("✗ Some tests failed!\n");
        return 1;
    }
}
