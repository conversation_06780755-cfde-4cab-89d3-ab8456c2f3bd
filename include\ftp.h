#ifndef FTP_H
#define FTP_H

#include "net.h"
#include "tcp.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

// FTP 端口定义
#define FTP_CONTROL_PORT 21
#define FTP_DATA_PORT_START 20000
#define FTP_DATA_PORT_END 21000

// FTP 响应码
#define FTP_READY "220 Simple FTP Server ready\r\n"
#define FTP_USER_OK "331 User name okay, need password\r\n"
#define FTP_LOGIN_OK "230 User logged in, proceed\r\n"
#define FTP_LOGIN_FAIL "530 Login incorrect\r\n"
#define FTP_PWD_REPLY "257 \"%s\" is current directory\r\n"
#define FTP_CWD_OK "250 Directory successfully changed\r\n"
#define FTP_CWD_FAIL "550 Failed to change directory\r\n"
#define FTP_PASV_OK "227 Entering Passive Mode (%d,%d,%d,%d,%d,%d)\r\n"
#define FTP_LIST_OK "150 Here comes the directory listing\r\n"
#define FTP_LIST_END "226 Directory send OK\r\n"
#define FTP_RETR_OK "150 Opening BINARY mode data connection for %s\r\n"
#define FTP_RETR_END "226 Transfer complete\r\n"
#define FTP_RETR_FAIL "550 Failed to open file\r\n"
#define FTP_STOR_OK "150 Ok to send data\r\n"
#define FTP_STOR_END "226 Transfer complete\r\n"
#define FTP_STOR_FAIL "550 Failed to create file\r\n"
#define FTP_QUIT "221 Goodbye\r\n"
#define FTP_UNKNOWN "500 Unknown command\r\n"
#define FTP_SYNTAX_ERROR "501 Syntax error in parameters or arguments\r\n"

// FTP 缓冲区大小
#define FTP_BUFFER_SIZE 1024
#define FTP_PATH_MAX 256
#define FTP_CMD_MAX 64
#define FTP_ARG_MAX 192

// FTP 连接状态
typedef enum {
    FTP_STATE_CONNECTED,
    FTP_STATE_USER_SENT,
    FTP_STATE_LOGGED_IN,
    FTP_STATE_PASV_MODE,
    FTP_STATE_DATA_TRANSFER
} ftp_state_t;

// FTP 连接信息
typedef struct {
    ftp_state_t state;
    char username[64];
    char current_dir[FTP_PATH_MAX];
    uint16_t data_port;
    tcp_conn_t *data_conn;
    FILE *transfer_file;
    int is_upload;
} ftp_session_t;

// FTP 命令结构
typedef struct {
    char cmd[FTP_CMD_MAX];
    char arg[FTP_ARG_MAX];
} ftp_command_t;

// FTP 服务器函数
void ftp_init();
void ftp_control_handler(tcp_conn_t *tcp_conn, uint8_t *data, size_t len, uint8_t *src_ip, uint16_t src_port);
void ftp_data_handler(tcp_conn_t *tcp_conn, uint8_t *data, size_t len, uint8_t *src_ip, uint16_t src_port);

// FTP 命令处理函数
void ftp_parse_command(const char *input, ftp_command_t *cmd);
void ftp_handle_user(tcp_conn_t *conn, ftp_session_t *session, const char *username, uint8_t *src_ip, uint16_t src_port);
void ftp_handle_pass(tcp_conn_t *conn, ftp_session_t *session, const char *password, uint8_t *src_ip, uint16_t src_port);
void ftp_handle_pwd(tcp_conn_t *conn, ftp_session_t *session, uint8_t *src_ip, uint16_t src_port);
void ftp_handle_cwd(tcp_conn_t *conn, ftp_session_t *session, const char *path, uint8_t *src_ip, uint16_t src_port);
void ftp_handle_pasv(tcp_conn_t *conn, ftp_session_t *session, uint8_t *src_ip, uint16_t src_port);
void ftp_handle_list(tcp_conn_t *conn, ftp_session_t *session, uint8_t *src_ip, uint16_t src_port);
void ftp_handle_retr(tcp_conn_t *conn, ftp_session_t *session, const char *filename, uint8_t *src_ip, uint16_t src_port);
void ftp_handle_stor(tcp_conn_t *conn, ftp_session_t *session, const char *filename, uint8_t *src_ip, uint16_t src_port);
void ftp_handle_quit(tcp_conn_t *conn, ftp_session_t *session, uint8_t *src_ip, uint16_t src_port);

// 工具函数
ftp_session_t *ftp_get_session(uint8_t *src_ip, uint16_t src_port);
void ftp_send_response(tcp_conn_t *conn, const char *response, uint8_t *dst_ip, uint16_t dst_port);
int ftp_check_file_permissions(const char *filepath, int is_write);
long ftp_get_file_size(const char *filepath);
void ftp_send_file_list(tcp_conn_t *data_conn, const char *directory, uint8_t *dst_ip, uint16_t dst_port);

// 全局变量声明
extern map_t ftp_session_table;

#endif
