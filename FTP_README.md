# 简易FTP服务器实现

本项目在现有TCP协议栈基础上实现了一个简易的FTP服务器，支持基本的文件上传和下载功能。

## 实现文件

### 核心实现
- `include/ftp.h` - FTP协议头文件定义
- `src/ftp.c` - FTP协议实现
- `app/ftp_server.c` - FTP服务器主程序

### 测试文件
- `testing/ftp_test.c` - FTP功能单元测试

## 功能特性

### 支持的FTP命令
- `USER` - 用户认证（支持anonymous、ftp用户）
- `PASS` - 密码认证
- `PWD` - 显示当前工作目录
- `CWD` - 改变工作目录
- `PASV` - 进入被动模式
- `LIST` - 列出目录内容
- `RETR` - 下载文件
- `STOR` - 上传文件
- `QUIT` - 退出连接

### 安全特性
- 用户认证机制
- 文件权限检查
- 路径安全验证
- 错误处理机制

## 编译和测试

### 编译FTP服务器
```bash
cd build
cmake ..
cmake --build . --target ftp_server
```

### 运行FTP服务器
```bash
.\ftp_server.exe
```

### 运行测试
```bash
ctest -R ftp_test
```

期望输出：
```
Test project C:/Users/<USER>/Desktop/net-lab/build
    Start 9: ftp_test
1/1 Test #9: ftp_test .........................   Passed    0.11 sec

100% tests passed, 0 tests failed out of 1
```

### 测试详情
```bash
.\ftp_test.exe
```

输出示例：
```
FTP Server Unit Tests
=====================

=== Testing FTP Command Parsing ===
✓ Parse USER command
✓ Parse PASS command
✓ Parse PWD command
✓ Command case conversion

=== Testing String Processing ===
✓ String comparison
✓ Case insensitive check
✓ String length
✓ Empty string
✓ String copy

=== Testing File Operations ===
✓ File creation and access
✓ File removal

=== Test Results ===
Tests passed: 11
Tests failed: 0
Total tests: 11
✓ All tests passed!
```

## 技术实现

- **协议栈集成**: 基于现有TCP协议栈实现
- **RFC 959标准**: 遵循FTP协议标准
- **会话管理**: 支持多客户端并发连接
- **数据传输**: 支持被动模式数据传输
- **错误处理**: 完善的错误处理机制

## 项目结构

```
├── include/ftp.h          # FTP协议定义
├── src/ftp.c             # FTP协议实现
├── app/ftp_server.c      # FTP服务器主程序
└── testing/ftp_test.c    # 单元测试程序
```

本实现成功展示了在现有网络协议栈基础上构建应用层协议的完整过程。
