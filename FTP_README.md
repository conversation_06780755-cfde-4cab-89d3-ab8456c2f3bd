# 简易FTP服务器实现

本项目在现有TCP协议栈基础上实现了一个简易的FTP服务器，支持基本的文件上传和下载功能。

## 功能特性

### 协议实现
- **FTP控制连接**：在端口21监听FTP控制命令
- **FTP数据连接**：支持被动模式(PASV)的数据传输
- **RFC 959标准**：遵循FTP协议标准实现

### 支持的FTP命令
- `USER` - 用户认证（用户名）
- `PASS` - 用户认证（密码）
- `PWD` - 显示当前工作目录
- `CWD` - 改变当前工作目录
- `PASV` - 进入被动模式
- `LIST` - 列出目录内容
- `RETR` - 下载文件
- `STOR` - 上传文件
- `QUIT` - 退出连接

### 文件管理功能
- **文件上传**：支持文件上传到服务器
- **文件下载**：支持从服务器下载文件
- **权限检查**：检查文件读写权限
- **完整性保证**：确保文件传输的完整性
- **安全性验证**：基本的路径和权限验证

## 编译和运行

### 编译
```bash
cd build
cmake ..
cmake --build . --target ftp_server
```

### 运行FTP服务器
```bash
./ftp_server.exe
```

服务器启动后会显示：
- 控制端口：21
- 数据端口范围：20000-21000
- 支持的用户：anonymous, ftp（无需密码）

### 运行测试
```bash
# 编译测试程序
cmake --build . --target ftp_test

# 运行测试
./ftp_test.exe

# 或使用Python测试脚本
python test_ftp_client.py
```

## 使用示例

### 使用标准FTP客户端连接
```bash
ftp 127.0.0.1 21
```

连接后的操作示例：
```
Connected to 127.0.0.1.
220 Simple FTP Server ready
Name (127.0.0.1:user): anonymous
331 User name okay, need password
Password: 
230 User logged in, proceed
ftp> pwd
257 "/" is current directory
ftp> pasv
227 Entering Passive Mode (127,0,0,1,78,32)
ftp> ls
150 Here comes the directory listing
226 Directory send OK
ftp> get test.txt
150 Opening BINARY mode data connection for test.txt
226 Transfer complete
ftp> put upload.txt
150 Ok to send data
226 Transfer complete
ftp> quit
221 Goodbye
```

### 使用Python测试脚本
```bash
python test_ftp_client.py
```

## 架构设计

### 文件结构
```
include/ftp.h          - FTP协议头文件定义
src/ftp.c             - FTP协议实现
app/ftp_server.c      - FTP服务器主程序
testing/ftp_test.c    - FTP功能测试程序
test_ftp_client.py    - Python测试客户端
```

### 核心组件

#### 1. FTP会话管理
- 维护客户端连接状态
- 管理用户认证信息
- 跟踪当前工作目录

#### 2. 命令处理
- 解析FTP命令和参数
- 验证命令语法和权限
- 生成标准FTP响应

#### 3. 数据传输
- 被动模式数据连接
- 文件上传/下载处理
- 数据完整性检查

#### 4. 安全机制
- 用户认证验证
- 文件权限检查
- 路径安全验证

## 测试验证

### 功能测试
1. **连接测试**：验证服务器启动和客户端连接
2. **认证测试**：测试用户登录功能
3. **目录操作**：测试PWD、CWD命令
4. **文件传输**：测试RETR、STOR命令
5. **错误处理**：测试无效命令和错误情况

### 测试结果
- ✅ 基本FTP命令支持
- ✅ 用户认证功能
- ✅ 目录操作功能
- ✅ 被动模式数据连接
- ✅ 文件权限检查
- ✅ 错误处理机制

### 性能特性
- **并发连接**：支持多个客户端同时连接
- **内存管理**：高效的会话和连接管理
- **错误恢复**：健壮的错误处理机制

## 技术实现细节

### 协议栈集成
- 基于现有TCP协议栈实现
- 复用TCP连接管理机制
- 利用现有的网络I/O框架

### 数据结构
- `ftp_session_t`：FTP会话信息
- `ftp_command_t`：FTP命令结构
- `tcp_conn_t`：TCP连接信息

### 关键算法
- FTP命令解析算法
- 被动模式端口分配
- 文件传输状态机

## 限制和改进方向

### 当前限制
- 仅支持被动模式数据传输
- 简化的用户认证机制
- 基本的文件权限检查

### 改进方向
1. **主动模式支持**：实现PORT命令
2. **安全增强**：SSL/TLS加密支持
3. **用户管理**：完整的用户权限系统
4. **性能优化**：大文件传输优化
5. **日志系统**：详细的操作日志记录

## 总结

本FTP服务器实现了RFC 959标准的核心功能，提供了完整的文件上传下载服务。通过在现有协议栈基础上的实现，展示了网络协议的分层设计思想，为进一步的功能扩展奠定了良好的基础。
