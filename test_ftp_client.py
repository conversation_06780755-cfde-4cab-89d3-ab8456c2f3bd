#!/usr/bin/env python3
"""
简单的FTP客户端测试脚本
用于测试我们实现的FTP服务器功能
"""

import socket
import time
import sys
import os

class SimpleFTPClient:
    def __init__(self, host='127.0.0.1', port=21):
        self.host = host
        self.port = port
        self.control_socket = None
        self.data_socket = None
        
    def connect(self):
        """连接到FTP服务器"""
        try:
            self.control_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.control_socket.connect((self.host, self.port))
            response = self.receive_response()
            print(f"Connected: {response}")
            return True
        except Exception as e:
            print(f"Connection failed: {e}")
            return False
    
    def send_command(self, command):
        """发送FTP命令"""
        if self.control_socket:
            cmd = command + '\r\n'
            self.control_socket.send(cmd.encode())
            print(f"Sent: {command}")
    
    def receive_response(self):
        """接收FTP响应"""
        if self.control_socket:
            response = self.control_socket.recv(1024).decode()
            print(f"Received: {response.strip()}")
            return response
        return ""
    
    def login(self, username='anonymous', password=''):
        """登录FTP服务器"""
        self.send_command(f'USER {username}')
        response = self.receive_response()
        
        if '331' in response:  # Need password
            self.send_command(f'PASS {password}')
            response = self.receive_response()
            return '230' in response  # Login successful
        
        return '230' in response
    
    def pwd(self):
        """获取当前目录"""
        self.send_command('PWD')
        return self.receive_response()
    
    def cwd(self, directory):
        """改变目录"""
        self.send_command(f'CWD {directory}')
        return self.receive_response()
    
    def pasv(self):
        """进入被动模式"""
        self.send_command('PASV')
        response = self.receive_response()
        
        # 解析PASV响应获取数据端口
        if '227' in response:
            # 提取端口信息 (h1,h2,h3,h4,p1,p2)
            start = response.find('(')
            end = response.find(')')
            if start != -1 and end != -1:
                parts = response[start+1:end].split(',')
                if len(parts) == 6:
                    port = int(parts[4]) * 256 + int(parts[5])
                    return port
        return None
    
    def list_files(self):
        """列出文件"""
        port = self.pasv()
        if port:
            self.send_command('LIST')
            response = self.receive_response()
            
            # 这里应该连接数据端口接收文件列表
            # 简化实现，只显示响应
            return response
        return None
    
    def download_file(self, filename):
        """下载文件"""
        port = self.pasv()
        if port:
            self.send_command(f'RETR {filename}')
            response = self.receive_response()
            return response
        return None
    
    def upload_file(self, filename):
        """上传文件"""
        port = self.pasv()
        if port:
            self.send_command(f'STOR {filename}')
            response = self.receive_response()
            return response
        return None
    
    def quit(self):
        """退出连接"""
        self.send_command('QUIT')
        response = self.receive_response()
        if self.control_socket:
            self.control_socket.close()
        return response
    
    def test_all_commands(self):
        """测试所有FTP命令"""
        print("=== FTP Server Test ===")
        
        # 测试连接
        if not self.connect():
            return False
        
        # 测试登录
        print("\n1. Testing login...")
        if self.login('anonymous', ''):
            print("✓ Login successful")
        else:
            print("✗ Login failed")
            return False
        
        # 测试PWD
        print("\n2. Testing PWD...")
        self.pwd()
        
        # 测试CWD
        print("\n3. Testing CWD...")
        self.cwd('/tmp')
        self.pwd()  # 验证目录是否改变
        
        # 测试PASV
        print("\n4. Testing PASV...")
        port = self.pasv()
        if port:
            print(f"✓ PASV mode enabled, data port: {port}")
        else:
            print("✗ PASV mode failed")
        
        # 测试LIST
        print("\n5. Testing LIST...")
        self.list_files()
        
        # 测试RETR
        print("\n6. Testing RETR...")
        self.download_file('test.txt')
        
        # 测试STOR
        print("\n7. Testing STOR...")
        self.upload_file('upload_test.txt')
        
        # 测试无效命令
        print("\n8. Testing invalid command...")
        self.send_command('INVALID')
        self.receive_response()
        
        # 测试QUIT
        print("\n9. Testing QUIT...")
        self.quit()
        
        print("\n=== Test completed ===")
        return True

def main():
    """主函数"""
    print("FTP Client Test Script")
    print("=====================")
    
    # 检查FTP服务器是否在运行
    print("Please make sure the FTP server is running on port 21")
    print("You can start it with: ./ftp_server.exe")
    input("Press Enter to continue...")
    
    # 创建测试文件
    test_file = "test_upload.txt"
    with open(test_file, 'w') as f:
        f.write("This is a test file for FTP upload.\n")
        f.write("Line 2 of the test file.\n")
        f.write("End of test file.\n")
    print(f"Created test file: {test_file}")
    
    # 运行测试
    client = SimpleFTPClient()
    success = client.test_all_commands()
    
    if success:
        print("\n✓ All tests completed successfully!")
    else:
        print("\n✗ Some tests failed!")
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"Cleaned up test file: {test_file}")

if __name__ == "__main__":
    main()
