#include "ip.h"

#include "arp.h"
#include "ethernet.h"
#include "icmp.h"
#include "net.h"

static uint16_t packet_id = 0;

/**
 * @brief 处理一个收到的数据包
 *
 * @param buf 要处理的数据包
 * @param src_mac 源mac地址
 */
void ip_in(buf_t *buf, uint8_t *src_mac) {
    // TO-DO
    // Step 1: 检查数据包长度
    if (buf->len < sizeof(ip_hdr_t)) {
        return; // 丢弃数据包
    }

    // Step 2: 进行报头检测
    ip_hdr_t *ip_hdr = (ip_hdr_t *)buf->data;
    if (ip_hdr->version != IP_VERSION_4 || swap16(ip_hdr->total_len16) > buf->len) {
        return; // 丢弃数据包
    }

    // Step 3: 校验头部校验和
    uint16_t original_checksum = ip_hdr->hdr_checksum16;
    ip_hdr->hdr_checksum16 = 0;
    if (checksum16((uint16_t *)ip_hdr, sizeof(ip_hdr_t)) != original_checksum) {
        return; // 丢弃数据包
    }
    ip_hdr->hdr_checksum16 = original_checksum;

    // Step 4: 对比目的 IP 地址
    if (memcmp(ip_hdr->dst_ip, net_if_ip, NET_IP_LEN) != 0) {
        return; // 丢弃数据包
    }

    // Step 5: 去除填充字段
    if (buf->len > swap16(ip_hdr->total_len16)) {
        buf_remove_padding(buf, buf->len - swap16(ip_hdr->total_len16));
    }

    // Step 6: 去掉 IP 报头
    buf_remove_header(buf, sizeof(ip_hdr_t));

    // Step 7: 向上层传递数据包
    if (net_in(buf, ip_hdr->protocol, ip_hdr->src_ip) == -1) {
        // 协议不可达，发送 ICMP 协议不可达信息
        buf_add_header(buf, sizeof(ip_hdr_t));
        icmp_unreachable(buf, ip_hdr->src_ip, ICMP_CODE_PROTOCOL_UNREACH);
    }
}
/**
 * @brief 处理一个要发送的ip分片
 *
 * @param buf 要发送的分片
 * @param ip 目标ip地址
 * @param protocol 上层协议
 * @param id 数据包id
 * @param offset 分片offset，必须被8整除
 * @param mf 分片mf标志，是否有下一个分片
 */
void ip_fragment_out(buf_t *buf, uint8_t *ip, net_protocol_t protocol, int id, uint16_t offset, int mf) {
    // TO-DO
    // Step 1: 增加 IP 头部空间 (20 字节)
    buf_add_header(buf, sizeof(ip_hdr_t));

    ip_hdr_t *ip_hdr = (ip_hdr_t *)buf->data;

    ip_hdr->version = IP_VERSION_4;
    ip_hdr->hdr_len = sizeof(ip_hdr_t) / 4;
    ip_hdr->tos = 0;
    ip_hdr->total_len16 = swap16(buf->len);
    ip_hdr->id16 = swap16(id);
    ip_hdr->flags_fragment16 = swap16((mf ? IP_MORE_FRAGMENT : 0) | offset);
    ip_hdr->ttl = IP_DEFALUT_TTL;
    ip_hdr->protocol = protocol;
    memcpy(ip_hdr->src_ip, net_if_ip, NET_IP_LEN);
    memcpy(ip_hdr->dst_ip, ip, NET_IP_LEN);

    ip_hdr->hdr_checksum16 = 0;
    ip_hdr->hdr_checksum16 = checksum16((uint16_t *)buf->data, sizeof(ip_hdr_t));

    arp_out(buf, ip);
}

/**
 * @brief 处理一个要发送的ip数据包
 *
 * @param buf 要处理的包
 * @param ip 目标ip地址
 * @param protocol 上层协议
 */
void ip_out(buf_t *buf, uint8_t *ip, net_protocol_t protocol) {
    // TO-DO
    size_t ip_len = ETHERNET_MAX_TRANSPORT_UNIT - sizeof(ip_hdr_t);
    static int id = 0;
    if(buf->len <= ip_len) {
        //不分片
        ip_fragment_out(buf, ip, protocol, id, 0, 0);
    }else{
        //分片
        buf_t ip_buf;
        uint16_t has_out = 0;
        uint16_t offset = 0;
        int mf;
        while(buf->len) {
            size_t fragment_len = (buf->len > ip_len) ? ip_len : buf->len;
            buf_init(&ip_buf, fragment_len);
            memcpy(ip_buf.data, buf->data, fragment_len);

            if(buf->len - fragment_len) mf = 1;
            else mf = 0;
            offset = has_out / IP_HDR_OFFSET_PER_BYTE;

            ip_fragment_out(&ip_buf, ip, protocol, id, offset, mf);

            has_out += fragment_len;
            buf->data += fragment_len;
            buf->len -= fragment_len;
        }
    }
    id++;
}

/**
 * @brief 初始化ip协议
 *
 */
void ip_init() {
    net_add_protocol(NET_PROTOCOL_IP, ip_in);
}